{% extends "base.html" %}

{% block title %}Agents - Web Scraper Pro{% endblock %}

{% block page_title %}Agent Management{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <button type="button" class="btn btn-outline-secondary" onclick="refreshAgents()">
        <i class="bi bi-arrow-clockwise"></i> Refresh
    </button>
    <button type="button" class="btn btn-outline-primary" onclick="restartAllAgents()">
        <i class="bi bi-bootstrap-reboot"></i> Restart All
    </button>
</div>
{% endblock %}

{% block content %}
<!-- Agent Overview Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-start-success">
            <div class="card-body">
                <h5 class="card-title text-success" id="active-agents-count">0</h5>
                <p class="card-text">Active Agents</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-start-warning">
            <div class="card-body">
                <h5 class="card-title text-warning" id="idle-agents-count">0</h5>
                <p class="card-text">Idle Agents</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-start-danger">
            <div class="card-body">
                <h5 class="card-title text-danger" id="error-agents-count">0</h5>
                <p class="card-text">Error Agents</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-start-primary">
            <div class="card-body">
                <h5 class="card-title text-primary" id="total-tasks-count">0</h5>
                <p class="card-text">Total Tasks</p>
            </div>
        </div>
    </div>
</div>

<!-- Agents Grid -->
<div class="row" id="agents-grid">
    <div class="col-12 text-center py-4">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading agents...</span>
        </div>
    </div>
</div>

<!-- Agent Details Modal -->
<div class="modal fade" id="agentDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Agent Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="agent-details-content">
                <!-- Agent details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-warning" id="restart-agent-btn" onclick="restartSelectedAgent()">
                    <i class="bi bi-bootstrap-reboot"></i> Restart Agent
                </button>
                <button type="button" class="btn btn-primary" id="configure-agent-btn" onclick="configureSelectedAgent()">
                    <i class="bi bi-gear"></i> Configure
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Agent Configuration Modal -->
<div class="modal fade" id="agentConfigModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Configure Agent</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="agent-config-form">
                    <div class="mb-3">
                        <label for="agent-name" class="form-label">Agent Name</label>
                        <input type="text" class="form-control" id="agent-name" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="agent-type" class="form-label">Agent Type</label>
                        <input type="text" class="form-control" id="agent-type" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="max-concurrent-tasks" class="form-label">Max Concurrent Tasks</label>
                        <input type="number" class="form-control" id="max-concurrent-tasks" min="1" max="10" value="3">
                    </div>
                    
                    <div class="mb-3">
                        <label for="timeout" class="form-label">Task Timeout (seconds)</label>
                        <input type="number" class="form-control" id="timeout" min="10" max="3600" value="300">
                    </div>
                    
                    <div class="mb-3">
                        <label for="retry-count" class="form-label">Retry Count</label>
                        <input type="number" class="form-control" id="retry-count" min="0" max="5" value="3">
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="auto-restart">
                            <label class="form-check-label" for="auto-restart">
                                Auto-restart on failure
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="debug-mode">
                            <label class="form-check-label" for="debug-mode">
                                Debug mode
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="custom-config" class="form-label">Custom Configuration (JSON)</label>
                        <textarea class="form-control" id="custom-config" rows="6" placeholder='{"key": "value"}'></textarea>
                        <div class="form-text">Enter additional configuration parameters in JSON format</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveAgentConfiguration()">Save Configuration</button>
            </div>
        </div>
    </div>
</div>

<!-- Performance Chart Modal -->
<div class="modal fade" id="performanceChartModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Agent Performance Metrics</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="chart-container">
                            <canvas id="agentCpuChart"></canvas>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="chart-container">
                            <canvas id="agentMemoryChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="chart-container">
                            <canvas id="agentTasksChart"></canvas>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="chart-container">
                            <canvas id="agentResponseTimeChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="/static/js/agents.js"></script>
{% endblock %}
