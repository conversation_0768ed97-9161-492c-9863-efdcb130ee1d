# Configuration for scraping Yelp business listings

url: "https://www.yelp.com/search?find_desc=Restaurants&find_loc=San+Francisco%2C+CA"
selectors:
  business_name: ".css-1m051bw"
  phone_number: ".css-1p9ibgf"
  location: ".css-qyp8bo"
  hours: ".css-1h1j0y3"
  rating: ".css-1fdy0l5"
  review_count: ".css-1e4fdj9"
output: "output/yelp_businesses.xlsx"
format: "excel"
max_pages: 5
render_js: true
anti_detection: true
clean_data: true

# Scraper settings
scraper:
  timeout: 60
  max_retries: 3
  respect_robots_txt: true

# Parser settings
parser:
  normalize_whitespace: true
  extract_metadata: true

# Storage settings
storage:
  pretty_json: true
  excel_engine: "openpyxl"
