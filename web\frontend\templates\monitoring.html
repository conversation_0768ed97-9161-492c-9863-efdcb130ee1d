{% extends "base.html" %}

{% block title %}Monitoring - Web Scraper Pro{% endblock %}

{% block page_title %}System Monitoring{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <button type="button" class="btn btn-outline-secondary" onclick="refreshMonitoring()">
        <i class="bi bi-arrow-clockwise"></i> Refresh
    </button>
    <div class="btn-group" role="group">
        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
            <i class="bi bi-clock"></i> Time Range
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#" onclick="changeTimeRange('1h')">Last Hour</a></li>
            <li><a class="dropdown-item" href="#" onclick="changeTimeRange('6h')">Last 6 Hours</a></li>
            <li><a class="dropdown-item" href="#" onclick="changeTimeRange('24h')">Last 24 Hours</a></li>
            <li><a class="dropdown-item" href="#" onclick="changeTimeRange('7d')">Last 7 Days</a></li>
        </ul>
    </div>
    <button type="button" class="btn btn-outline-primary" onclick="exportMetrics()">
        <i class="bi bi-download"></i> Export
    </button>
</div>
{% endblock %}

{% block content %}
<!-- System Health Overview -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start-primary">
            <div class="card-body metric-card">
                <div class="metric-value text-primary" id="system-uptime">0h 0m</div>
                <div class="metric-label">System Uptime</div>
                <div class="metric-change">
                    <span class="status-dot online"></span> Online
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start-success">
            <div class="card-body metric-card">
                <div class="metric-value text-success" id="cpu-usage-metric">0%</div>
                <div class="metric-label">CPU Usage</div>
                <div class="metric-change" id="cpu-change">
                    <i class="bi bi-arrow-up"></i> Normal
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start-warning">
            <div class="card-body metric-card">
                <div class="metric-value text-warning" id="memory-usage-metric">0%</div>
                <div class="metric-label">Memory Usage</div>
                <div class="metric-change" id="memory-change">
                    <i class="bi bi-arrow-up"></i> Normal
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start-info">
            <div class="card-body metric-card">
                <div class="metric-value text-info" id="requests-per-second">0</div>
                <div class="metric-label">Requests/sec</div>
                <div class="metric-change" id="requests-change">
                    <i class="bi bi-activity"></i> Active
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Performance Charts -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i> System Performance
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="systemPerformanceChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-hdd"></i> Resource Usage
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="resourceUsageChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Network and Application Metrics -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-wifi"></i> Network Activity
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="networkChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-speedometer2"></i> Response Times
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="responseTimeChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Alerts and Logs -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-exclamation-triangle"></i> Recent Alerts
                </h5>
                <span class="badge bg-danger" id="alert-count">0</span>
            </div>
            <div class="card-body">
                <div id="alerts-list" style="max-height: 300px; overflow-y: auto;">
                    <div class="text-center py-4">
                        <i class="bi bi-check-circle text-success display-6"></i>
                        <p class="text-muted mt-2">No alerts</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-journal-text"></i> System Logs
                </h5>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-secondary" onclick="pauseLogs()" id="pause-logs-btn">
                        <i class="bi bi-pause"></i>
                    </button>
                    <button class="btn btn-outline-secondary" onclick="clearLogs()">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="logs-container" style="max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 0.875rem;">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading logs...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Metrics Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-table"></i> Detailed Metrics
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Metric</th>
                                <th>Current Value</th>
                                <th>Average (1h)</th>
                                <th>Peak (1h)</th>
                                <th>Status</th>
                                <th>Trend</th>
                            </tr>
                        </thead>
                        <tbody id="metrics-table-body">
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading metrics...</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="/static/js/monitoring.js"></script>
{% endblock %}
