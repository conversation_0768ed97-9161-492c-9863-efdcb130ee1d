/* Custom Dashboard Styles */

:root {
    --primary-color: #0d6efd;
    --primary-dark: #0b5ed7;
    --primary-light: #6ea8fe;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;

    /* Enhanced color palette */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #adb5bd;

    /* Shadows and borders */
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    --border-radius: 0.5rem;
    --border-radius-sm: 0.25rem;
    --border-radius-lg: 0.75rem;

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: all var(--transition-normal);
}

/* Enhanced Navigation Styles */
.navbar {
    box-shadow: var(--shadow-sm);
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
    transition: all var(--transition-fast);
}

.navbar-brand:hover {
    transform: scale(1.05);
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 1.5rem 0 0;
    background: var(--bg-primary);
    box-shadow: var(--shadow-md);
    border-right: 1px solid var(--bg-tertiary);
    transition: all var(--transition-normal);
}

.sidebar .nav-link {
    color: var(--text-secondary);
    padding: 0.875rem 1.25rem;
    border-radius: var(--border-radius-sm);
    margin: 0.25rem 0.75rem;
    font-weight: 500;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.sidebar .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left var(--transition-normal);
}

.sidebar .nav-link:hover::before {
    left: 100%;
}

.sidebar .nav-link:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    transform: translateX(4px);
    box-shadow: var(--shadow-sm);
}

.sidebar .nav-link.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: var(--shadow-md);
    transform: translateX(4px);
}

.sidebar .nav-link.active::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 60%;
    background: rgba(255,255,255,0.8);
    border-radius: 2px;
}

.sidebar .nav-link i {
    margin-right: 0.75rem;
    font-size: 1.1rem;
    transition: all var(--transition-fast);
}

.sidebar .nav-link:hover i,
.sidebar .nav-link.active i {
    transform: scale(1.1);
}

.sidebar-heading {
    font-size: .75rem;
    text-transform: uppercase;
}

/* Main content */
main {
    padding-top: 56px;
}

/* Enhanced Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    background: var(--bg-primary);
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--info-color), var(--success-color));
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px);
}

.card:hover::before {
    opacity: 1;
}

.card-header {
    background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
    border-bottom: 1px solid var(--bg-tertiary);
    padding: 1.25rem 1.5rem;
    font-weight: 600;
    position: relative;
}

.card-title {
    margin-bottom: 0;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.card-body {
    padding: 1.5rem;
}

/* Status indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.status-dot.online {
    background-color: var(--success-color);
    animation: pulse 2s infinite;
}

.status-dot.offline {
    background-color: var(--danger-color);
}

.status-dot.warning {
    background-color: var(--warning-color);
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(25, 135, 84, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(25, 135, 84, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(25, 135, 84, 0);
    }
}

/* Progress bars */
.progress {
    height: 0.75rem;
    border-radius: 0.375rem;
}

.progress-bar {
    border-radius: 0.375rem;
}

/* Job status badges */
.job-status {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
}

.job-status.pending {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.job-status.running {
    background-color: var(--info-color);
    color: var(--dark-color);
}

.job-status.completed {
    background-color: var(--success-color);
    color: white;
}

.job-status.failed {
    background-color: var(--danger-color);
    color: white;
}

.job-status.cancelled {
    background-color: var(--secondary-color);
    color: white;
}

/* Enhanced Loading overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(13, 110, 253, 0.1), rgba(0, 0, 0, 0.3));
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    animation: fadeIn var(--transition-fast) ease-out;
}

/* Component-specific loading states */
.loading-state {
    position: relative;
    pointer-events: none;
    opacity: 0.6;
    transition: opacity var(--transition-fast);
}

.loading-state::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2rem;
    height: 2rem;
    margin: -1rem 0 0 -1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1000;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.loading-state::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.7);
    z-index: 999;
    border-radius: inherit;
}

/* Page transition overlay */
.page-transition-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.95));
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    animation: fadeIn var(--transition-fast) ease-out;
}

.transition-spinner {
    text-align: center;
}

.transition-spinner .spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.3em;
}

/* Enhanced Metrics cards */
.metric-card {
    text-align: center;
    padding: 2rem 1.5rem;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity var(--transition-slow);
}

.metric-card:hover::before {
    opacity: 1;
}

.metric-value {
    font-size: 2.75rem;
    font-weight: 800;
    margin-bottom: 0.75rem;
    line-height: 1;
    background: linear-gradient(135deg, currentColor, rgba(currentColor, 0.7));
    -webkit-background-clip: text;
    background-clip: text;
    transition: all var(--transition-normal);
}

.metric-card:hover .metric-value {
    transform: scale(1.05);
}

.metric-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.75rem;
}

.metric-change {
    font-size: 0.8rem;
    color: var(--text-muted);
    font-weight: 500;
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius-sm);
    background: var(--bg-secondary);
    display: inline-block;
    transition: all var(--transition-fast);
}

.metric-change.positive {
    color: var(--success-color);
    background: rgba(25, 135, 84, 0.1);
}

.metric-change.negative {
    color: var(--danger-color);
    background: rgba(220, 53, 69, 0.1);
}

.metric-change.neutral {
    color: var(--text-muted);
    background: var(--bg-secondary);
}

/* Tables */
.table {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table thead th {
    border-bottom: 2px solid var(--primary-color);
    background-color: var(--light-color);
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.025);
}

/* Buttons */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Forms */
.form-control, .form-select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Charts container */
.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .sidebar {
        display: none !important;
    }
    
    main {
        margin-left: 0 !important;
    }
    
    .metric-card {
        padding: 1rem;
    }
    
    .metric-value {
        font-size: 1.5rem;
    }
}

/* Enhanced Animation classes */
.fade-in {
    animation: fadeIn var(--transition-normal) ease-out;
}

.fade-in-up {
    animation: fadeInUp var(--transition-normal) ease-out;
}

.fade-in-down {
    animation: fadeInDown var(--transition-normal) ease-out;
}

.slide-in {
    animation: slideIn var(--transition-normal) ease-out;
}

.slide-in-right {
    animation: slideInRight var(--transition-normal) ease-out;
}

.bounce-in {
    animation: bounceIn var(--transition-slow) ease-out;
}

.scale-in {
    animation: scaleIn var(--transition-normal) ease-out;
}

.rotate-in {
    animation: rotateIn var(--transition-normal) ease-out;
}

/* Staggered animations */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }

/* Keyframe definitions */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes rotateIn {
    from {
        opacity: 0;
        transform: rotate(-180deg) scale(0.8);
    }
    to {
        opacity: 1;
        transform: rotate(0deg) scale(1);
    }
}

/* Loading animations */
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.loading-dots::after {
    content: '';
    animation: dots 1.5s steps(5, end) infinite;
}

@keyframes dots {
    0%, 20% {
        color: rgba(0,0,0,0);
        text-shadow:
            .25em 0 0 rgba(0,0,0,0),
            .5em 0 0 rgba(0,0,0,0);
    }
    40% {
        color: var(--text-primary);
        text-shadow:
            .25em 0 0 rgba(0,0,0,0),
            .5em 0 0 rgba(0,0,0,0);
    }
    60% {
        text-shadow:
            .25em 0 0 var(--text-primary),
            .5em 0 0 rgba(0,0,0,0);
    }
    80%, 100% {
        text-shadow:
            .25em 0 0 var(--text-primary),
            .5em 0 0 var(--text-primary);
    }
}

/* Utility classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.border-start-primary {
    border-left: 4px solid var(--primary-color) !important;
}

.border-start-success {
    border-left: 4px solid var(--success-color) !important;
}

.border-start-warning {
    border-left: 4px solid var(--warning-color) !important;
}

.border-start-danger {
    border-left: 4px solid var(--danger-color) !important;
}
