{% extends "base.html" %}

{% block title %}Jobs - Web Scraper Pro{% endblock %}

{% block page_title %}Job Management{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createJobModal">
        <i class="bi bi-plus-circle"></i> Create Job
    </button>
    <button type="button" class="btn btn-outline-secondary" onclick="refreshJobs()">
        <i class="bi bi-arrow-clockwise"></i> Refresh
    </button>
    <div class="btn-group" role="group">
        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
            <i class="bi bi-funnel"></i> Filter
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#" onclick="filterJobs('all')">All Jobs</a></li>
            <li><a class="dropdown-item" href="#" onclick="filterJobs('running')">Running</a></li>
            <li><a class="dropdown-item" href="#" onclick="filterJobs('completed')">Completed</a></li>
            <li><a class="dropdown-item" href="#" onclick="filterJobs('failed')">Failed</a></li>
            <li><a class="dropdown-item" href="#" onclick="filterJobs('pending')">Pending</a></li>
        </ul>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Job Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary" id="total-jobs-count">0</h5>
                <p class="card-text">Total Jobs</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info" id="running-jobs-count">0</h5>
                <p class="card-text">Running</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success" id="completed-jobs-count">0</h5>
                <p class="card-text">Completed</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-danger" id="failed-jobs-count">0</h5>
                <p class="card-text">Failed</p>
            </div>
        </div>
    </div>
</div>

<!-- Jobs Table -->
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-task"></i> Jobs
                </h5>
            </div>
            <div class="col-auto">
                <div class="input-group input-group-sm">
                    <input type="text" class="form-control" placeholder="Search jobs..." id="job-search">
                    <button class="btn btn-outline-secondary" type="button" onclick="searchJobs()">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div id="jobs-table-container">
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading jobs...</span>
                </div>
            </div>
        </div>
        
        <!-- Pagination -->
        <nav aria-label="Jobs pagination" class="mt-3">
            <ul class="pagination justify-content-center" id="jobs-pagination">
                <!-- Pagination will be generated by JavaScript -->
            </ul>
        </nav>
    </div>
</div>

<!-- Create Job Modal -->
<div class="modal fade" id="createJobModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Scraping Job</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="create-job-form">
                    <!-- Basic Information -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="job-name" class="form-label">Job Name *</label>
                            <input type="text" class="form-control" id="job-name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="job-type" class="form-label">Job Type *</label>
                            <select class="form-select" id="job-type" required>
                                <option value="">Select job type...</option>
                                <option value="web_scraping">Web Scraping</option>
                                <option value="data_extraction">Data Extraction</option>
                                <option value="api_scraping">API Scraping</option>
                                <option value="document_processing">Document Processing</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="job-description" class="form-label">Description</label>
                        <textarea class="form-control" id="job-description" rows="2"></textarea>
                    </div>
                    
                    <!-- URL Configuration -->
                    <div class="mb-3">
                        <label for="target-url" class="form-label">Target URL *</label>
                        <input type="url" class="form-control" id="target-url" required>
                        <div class="form-text">Enter the URL you want to scrape</div>
                    </div>
                    
                    <!-- CSS Selectors -->
                    <div class="mb-3">
                        <label class="form-label">CSS Selectors</label>
                        <div id="selectors-container">
                            <div class="row mb-2">
                                <div class="col-md-4">
                                    <input type="text" class="form-control" placeholder="Field name" name="selector-name">
                                </div>
                                <div class="col-md-6">
                                    <input type="text" class="form-control" placeholder="CSS selector" name="selector-value">
                                </div>
                                <div class="col-md-2">
                                    <button type="button" class="btn btn-outline-success btn-sm" onclick="addSelector()">
                                        <i class="bi bi-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Advanced Options -->
                    <div class="accordion mb-3" id="advancedOptions">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#advancedCollapse">
                                    Advanced Options
                                </button>
                            </h2>
                            <div id="advancedCollapse" class="accordion-collapse collapse" data-bs-parent="#advancedOptions">
                                <div class="accordion-body">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="max-pages" class="form-label">Max Pages</label>
                                            <input type="number" class="form-control" id="max-pages" value="1" min="1">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="delay" class="form-label">Delay (seconds)</label>
                                            <input type="number" class="form-control" id="delay" value="1" min="0" step="0.1">
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="output-format" class="form-label">Output Format</label>
                                            <select class="form-select" id="output-format">
                                                <option value="json">JSON</option>
                                                <option value="csv">CSV</option>
                                                <option value="excel">Excel</option>
                                                <option value="xml">XML</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="priority" class="form-label">Priority</label>
                                            <select class="form-select" id="priority">
                                                <option value="normal">Normal</option>
                                                <option value="high">High</option>
                                                <option value="low">Low</option>
                                                <option value="urgent">Urgent</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-12">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="render-js">
                                                <label class="form-check-label" for="render-js">
                                                    Render JavaScript
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="anti-detection">
                                                <label class="form-check-label" for="anti-detection">
                                                    Use Anti-Detection
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="clean-data">
                                                <label class="form-check-label" for="clean-data">
                                                    Clean Data
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="createJob()">Create Job</button>
            </div>
        </div>
    </div>
</div>

<!-- Job Details Modal -->
<div class="modal fade" id="jobDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Job Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="job-details-content">
                <!-- Job details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-danger" id="cancel-job-btn" onclick="cancelSelectedJob()">Cancel Job</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="/static/js/jobs.js"></script>
{% endblock %}
