{% extends "base.html" %}

{% block title %}Data Management - Web Scraper Pro{% endblock %}

{% block page_title %}Data Management{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <button type="button" class="btn btn-primary" onclick="exportData()">
        <i class="bi bi-download"></i> Export Data
    </button>
    <button type="button" class="btn btn-outline-secondary" onclick="refreshData()">
        <i class="bi bi-arrow-clockwise"></i> Refresh
    </button>
    <div class="btn-group" role="group">
        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
            <i class="bi bi-funnel"></i> Filter
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#" onclick="filterByFormat('all')">All Formats</a></li>
            <li><a class="dropdown-item" href="#" onclick="filterByFormat('json')">JSON</a></li>
            <li><a class="dropdown-item" href="#" onclick="filterByFormat('csv')">CSV</a></li>
            <li><a class="dropdown-item" href="#" onclick="filterByFormat('excel')">Excel</a></li>
        </ul>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Data Overview Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start-primary">
            <div class="card-body metric-card">
                <div class="metric-value text-primary" id="total-records">0</div>
                <div class="metric-label">Total Records</div>
                <div class="metric-change">
                    <i class="bi bi-database"></i> All time
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start-success">
            <div class="card-body metric-card">
                <div class="metric-value text-success" id="total-datasets">0</div>
                <div class="metric-label">Datasets</div>
                <div class="metric-change">
                    <i class="bi bi-collection"></i> Available
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start-warning">
            <div class="card-body metric-card">
                <div class="metric-value text-warning" id="data-size">0 MB</div>
                <div class="metric-label">Total Size</div>
                <div class="metric-change">
                    <i class="bi bi-hdd"></i> Storage used
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start-info">
            <div class="card-body metric-card">
                <div class="metric-value text-info" id="recent-exports">0</div>
                <div class="metric-label">Recent Exports</div>
                <div class="metric-change">
                    <i class="bi bi-download"></i> Last 24h
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Data Sources and Formats -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-table"></i> Available Datasets
                </h5>
            </div>
            <div class="card-body">
                <div id="datasets-table-container">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading datasets...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-pie-chart"></i> Data by Format
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="dataFormatChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Data Preview and Export Options -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-eye"></i> Data Preview
                </h5>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-secondary" onclick="togglePreviewMode('table')" id="table-view-btn">
                        <i class="bi bi-table"></i> Table
                    </button>
                    <button class="btn btn-outline-secondary" onclick="togglePreviewMode('json')" id="json-view-btn">
                        <i class="bi bi-code"></i> JSON
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="data-preview-container">
                    <div class="text-center py-4">
                        <i class="bi bi-inbox display-4 text-muted"></i>
                        <p class="text-muted mt-2">Select a dataset to preview</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Export History -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i> Export History
                </h5>
            </div>
            <div class="card-body">
                <div id="export-history-container">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading export history...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Export Data Modal -->
<div class="modal fade" id="exportDataModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Export Data</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="export-form">
                    <div class="mb-3">
                        <label for="export-dataset" class="form-label">Select Dataset *</label>
                        <select class="form-select" id="export-dataset" required>
                            <option value="">Choose dataset...</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="export-format" class="form-label">Export Format *</label>
                        <select class="form-select" id="export-format" required>
                            <option value="json">JSON</option>
                            <option value="csv">CSV</option>
                            <option value="excel">Excel (XLSX)</option>
                            <option value="xml">XML</option>
                            <option value="sqlite">SQLite Database</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="export-filename" class="form-label">Filename</label>
                        <input type="text" class="form-control" id="export-filename" placeholder="Auto-generated if empty">
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="export-limit" class="form-label">Record Limit</label>
                            <input type="number" class="form-control" id="export-limit" placeholder="All records" min="1">
                        </div>
                        <div class="col-md-6">
                            <label for="export-offset" class="form-label">Start From Record</label>
                            <input type="number" class="form-control" id="export-offset" value="0" min="0">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="include-metadata">
                            <label class="form-check-label" for="include-metadata">
                                Include metadata (timestamps, job info, etc.)
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="compress-export">
                            <label class="form-check-label" for="compress-export">
                                Compress export file (ZIP)
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="export-filters" class="form-label">Filters (JSON)</label>
                        <textarea class="form-control" id="export-filters" rows="3" placeholder='{"field": "value"}'></textarea>
                        <div class="form-text">Optional: Filter records using JSON criteria</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="performExport()">
                    <i class="bi bi-download"></i> Export Data
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Data Details Modal -->
<div class="modal fade" id="dataDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Dataset Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="data-details-content">
                <!-- Dataset details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="exportSelectedDataset()">
                    <i class="bi bi-download"></i> Export This Dataset
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="/static/js/data.js"></script>
{% endblock %}
