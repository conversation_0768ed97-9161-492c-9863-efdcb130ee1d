# Multi-Agent Web Scraping System Environment Configuration
# Copy this file to .env and update the values

# =============================================================================
# WEB SERVER CONFIGURATION
# =============================================================================

# Server host and port
WEB_HOST=0.0.0.0
WEB_PORT=8000
WEB_DEBUG=false
WEB_RELOAD=false
WEB_WORKERS=1

# Application settings
WEB_TITLE="Multi-Agent Web Scraping System"
WEB_DESCRIPTION="Advanced web scraping with AI agents"
WEB_VERSION="2.0.0"

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# JWT Secret Key (CHANGE THIS IN PRODUCTION!)
SECRET_KEY=your-super-secret-key-change-in-production

# Authentication settings
WEB_ENABLE_AUTH=true
WEB_ACCESS_TOKEN_EXPIRE_MINUTES=30
WEB_REFRESH_TOKEN_EXPIRE_DAYS=7

# Password requirements
WEB_PASSWORD_MIN_LENGTH=8
WEB_REQUIRE_EMAIL_VERIFICATION=false
WEB_MAX_LOGIN_ATTEMPTS=5
WEB_LOCKOUT_DURATION_MINUTES=15

# =============================================================================
# AI & LANGUAGE MODEL CONFIGURATION
# =============================================================================

# OpenAI API Key for LangChain integration
OPENAI_API_KEY=your-openai-api-key-here

# Alternative AI providers (optional)
ANTHROPIC_API_KEY=your-anthropic-key-here
HUGGINGFACE_API_KEY=your-huggingface-key-here

# LangChain settings
LANGCHAIN_TRACING_V2=false
LANGCHAIN_API_KEY=your-langchain-api-key-here

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Primary database URL
DATABASE_URL=sqlite:///./webscraper.db

# Alternative database configurations (uncomment as needed)
# PostgreSQL
# DATABASE_URL=postgresql://username:password@localhost:5432/webscraper

# MySQL
# DATABASE_URL=mysql://username:password@localhost:3306/webscraper

# Database connection settings
DB_ECHO=false
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis URL for caching and sessions
REDIS_URL=redis://localhost:6379/0

# Redis connection settings
REDIS_PASSWORD=
REDIS_MAX_CONNECTIONS=10
REDIS_SOCKET_TIMEOUT=5
REDIS_SOCKET_CONNECT_TIMEOUT=5
REDIS_RETRY_ON_TIMEOUT=true

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/disable major features
WEB_ENABLE_AUTH=true
WEB_ENABLE_RATE_LIMITING=true
WEB_ENABLE_MONITORING=true
WEB_ENABLE_WEBSOCKETS=true
WEB_ENABLE_FILE_UPLOADS=true

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================

# Rate limiting settings
WEB_RATE_LIMIT_REQUESTS_PER_MINUTE=60
WEB_RATE_LIMIT_REQUESTS_PER_HOUR=1000
WEB_RATE_LIMIT_BURST_SIZE=10

# =============================================================================
# CORS CONFIGURATION
# =============================================================================

# CORS allowed origins (comma-separated)
CORS_ORIGINS=*

# CORS settings
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=*
CORS_ALLOW_HEADERS=*
CORS_MAX_AGE=600

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Log format
LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Log file path (optional)
LOG_FILE=logs/webscraper.log

# =============================================================================
# MONITORING & METRICS
# =============================================================================

# Monitoring settings
WEB_MONITORING_ENABLED=true
WEB_METRICS_ENDPOINT=/metrics
WEB_HEALTH_ENDPOINT=/health
WEB_MONITORING_UPDATE_INTERVAL=5
WEB_MONITORING_RETENTION_DAYS=30

# Alert thresholds
ALERT_CPU_THRESHOLD=80.0
ALERT_MEMORY_THRESHOLD=85.0
ALERT_DISK_THRESHOLD=90.0
ALERT_ERROR_RATE_THRESHOLD=5.0

# =============================================================================
# WEBSOCKET CONFIGURATION
# =============================================================================

# WebSocket settings
WS_MAX_CONNECTIONS=100
WS_PING_INTERVAL=20
WS_PING_TIMEOUT=10
WS_CLOSE_TIMEOUT=10
WS_MAX_MESSAGE_SIZE=1048576

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================

# Upload settings
UPLOAD_DIR=uploads
MAX_UPLOAD_SIZE=10485760
ALLOWED_EXTENSIONS=.json,.csv,.xlsx,.pdf,.txt,.html

# =============================================================================
# AGENT SYSTEM CONFIGURATION
# =============================================================================

# Agent system settings
AGENT_TIMEOUT=300
MAX_CONCURRENT_JOBS=10
JOB_CLEANUP_INTERVAL=3600

# Agent-specific settings
SCRAPER_AGENT_TIMEOUT=60
SCRAPER_AGENT_RETRIES=3
SCRAPER_AGENT_DELAY=1

PARSER_AGENT_TIMEOUT=30
PARSER_AGENT_MAX_CONTENT_SIZE=10485760

STORAGE_AGENT_TIMEOUT=60
STORAGE_AGENT_BATCH_SIZE=1000

# =============================================================================
# PROXY CONFIGURATION (Optional)
# =============================================================================

# HTTP proxy settings (optional)
HTTP_PROXY=
HTTPS_PROXY=
NO_PROXY=localhost,127.0.0.1

# =============================================================================
# EXTERNAL SERVICES (Optional)
# =============================================================================

# Email service (for notifications)
SMTP_HOST=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_USE_TLS=true

# Webhook URLs (for notifications)
SLACK_WEBHOOK_URL=
DISCORD_WEBHOOK_URL=

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Development mode settings
ENVIRONMENT=development
DEBUG_MODE=false
TESTING_MODE=false

# Development database (optional)
DEV_DATABASE_URL=sqlite:///./dev_webscraper.db

# Test database (optional)
TEST_DATABASE_URL=sqlite:///:memory:

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================

# Production-specific settings (uncomment for production)
# ENVIRONMENT=production
# DEBUG_MODE=false
# WEB_DEBUG=false
# LOG_LEVEL=WARNING

# Production database
# DATABASE_URL=***************************************************/webscraper_prod

# Production Redis
# REDIS_URL=redis://prod_redis_host:6379/0

# Production security
# SECRET_KEY=your-production-secret-key-here
# WEB_ENABLE_AUTH=true
# WEB_ENABLE_RATE_LIMITING=true

# =============================================================================
# CUSTOM CONFIGURATION
# =============================================================================

# Add your custom environment variables here
# CUSTOM_SETTING=value
